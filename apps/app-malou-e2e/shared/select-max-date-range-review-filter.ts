import { Page } from '@playwright/test';

export async function selectMaxDateRangeReviewFilter({ page }: { page: Page }) {
    await page.getByTestId('reviews-filters-btn').click();
    await page.getByTestId('reviews-filters-period-input').click();
    await page.getByTestId('period-date-0-btn').click();
    await page
        .locator('body')
        .first()
        .click({
            force: true,
            position: {
                x: 0,
                y: 0,
            },
        });
}
