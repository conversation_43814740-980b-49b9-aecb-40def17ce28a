import { TestBed } from '@angular/core/testing';
import { TranslateFakeLoader, TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';

import { FormatDatePipe } from './format-date.pipe';

describe('FormatDatePipe', () => {
    let translateService: any;

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [
                TranslateModule.forRoot({
                    loader: {
                        provide: TranslateLoader,
                        useClass: TranslateFakeLoader,
                    },
                }),
            ],
            providers: [TranslateService],
        }).compileComponents();

        translateService = TestBed.get(TranslateService);
    });

    [
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'fr',
            expected: 'Samedi 15 juin 2024, 09:12',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'en',
            expected: 'Saturday, Jun 15, 2024, 09:12 AM',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'es',
            expected: 'Sábado, 15 de jun de 2024, 09:12',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'it',
            expected: 'Sabato 15 giu 2024, 09:12',
        },
    ].forEach((value) => {
        it(`should format date correctly with full format and locale ${value.locale}`, () => {
            translateService.currentLang = value.locale;
            const pipe = new FormatDatePipe(translateService);

            const date = value.date;

            expect(pipe.transform(date, 'full')).toBe(value.expected);
        });
    });
    [
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'fr',
            expected: '15 juin 2024, 9:12:26',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'en',
            expected: 'Jun 15, 2024, 9:12:26 AM',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'es',
            expected: '15 jun 2024, 9:12:26',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'it',
            expected: '15 giu 2024, 9:12:26',
        },
    ].forEach((value) => {
        it(`should format date correctly with medium format and locale ${value.locale}`, () => {
            translateService.currentLang = value.locale;
            const pipe = new FormatDatePipe(translateService);

            const date = value.date;

            expect(pipe.transform(date, 'medium')).toBe(value.expected);
        });
    });

    [
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'fr',
            expected: 'Samedi 15 juin',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'en',
            expected: 'Saturday, Jun 15',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'es',
            expected: 'Sábado, 15 jun',
        },
        {
            date: new Date('2024-06-15T09:12:26'),
            locale: 'it',
            expected: 'Sabato 15 giu',
        },
    ].forEach((value) => {
        it(`should format date correctly with day format and locale ${value.locale}`, () => {
            translateService.currentLang = value.locale;
            const pipe = new FormatDatePipe(translateService);

            const date = value.date;

            expect(pipe.transform(date, 'day')).toBe(value.expected);
        });
    });

    [
        {
            date: new Date('2024-05-27T00:00:00'),
            locale: 'fr',
            expected: '27/05/2024',
        },
        {
            date: new Date('2024-05-27T00:00:00'),
            locale: 'en',
            expected: '5/27/2024',
        },
        {
            date: new Date('2024-05-27T00:00:00'),
            locale: 'es',
            expected: '27/5/2024',
        },
        {
            date: new Date('2024-05-27T00:00:00'),
            locale: 'it',
            expected: '27/05/2024',
        },
    ].forEach((value) => {
        it(`should format date correctly with shortDate format with locale ${value.locale}`, () => {
            translateService.currentLang = value.locale;
            const pipe = new FormatDatePipe(translateService);

            const date = value.date;

            expect(pipe.transform(date, 'shortDate')).toBe(value.expected);
        });
    });

    [
        {
            date: new Date('2024-05-27T14:12:26'),
            locale: 'fr',
            expected: '27 mai 2024',
        },
        {
            date: new Date('2024-05-27T14:12:26'),
            locale: 'en',
            expected: 'May 27, 2024',
        },
        {
            date: new Date('2024-05-27T14:12:26'),
            locale: 'es',
            expected: '27 may 2024',
        },
        {
            date: new Date('2024-05-27T14:12:26'),
            locale: 'it',
            expected: '27 mag 2024',
        },
    ].forEach((value) => {
        it(`should format date correctly with mediumDate format with locale ${value.locale}`, () => {
            translateService.currentLang = value.locale;
            const pipe = new FormatDatePipe(translateService);

            const date = value.date;

            expect(pipe.transform(date, 'mediumDate')).toBe(value.expected);
        });
    });

    [
        {
            date: new Date('2024-05-27T14:12:26'),
            locale: 'fr',
            expected: '14:12',
        },
        {
            date: new Date('2024-05-27T09:12:26'),
            locale: 'en',
            expected: '09:12 AM',
        },
        {
            date: new Date('2024-05-27T14:12:26'),
            locale: 'en',
            expected: '02:12 PM',
        },
        {
            date: new Date('2024-05-27T14:12:26'),
            locale: 'es',
            expected: '14:12',
        },
        {
            date: new Date('2024-05-27T14:12:26'),
            locale: 'it',
            expected: '14:12',
        },
    ].forEach((value) => {
        it(`should format date correctly with shortTime format with locale ${value.locale}`, () => {
            translateService.currentLang = value.locale;
            const pipe = new FormatDatePipe(translateService);

            const date = value.date;

            expect(pipe.transform(date, 'shortTime')).toBe(value.expected);
        });
    });
});
