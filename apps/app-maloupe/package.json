{"dependencies": {"@angular/animations": "^19.2.9", "@angular/cdk": "^18.2.0", "@angular/common": "^19.2.9", "@angular/compiler": "^19.2.9", "@angular/core": "^19.2.9", "@angular/forms": "^19.2.9", "@angular/material": "^18.2.0", "@angular/platform-browser": "^19.2.9", "@angular/platform-browser-dynamic": "^19.2.9", "@angular/router": "^19.2.9", "@malou-io/package-dto": "workspace:*", "@malou-io/package-utils": "workspace:*", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "@types/lodash.intersection": "^4.4.9", "canvas-confetti": "^1.9.2", "chart.js": "^4.1.2", "chartjs-adapter-luxon": "^1.3.0", "lodash.groupby": "^4.6.0", "lodash.intersection": "^4.4.0", "lodash.isstring": "^4.0.1", "lodash.mean": "^4.1.0", "lodash.sortedindexby": "^4.6.0", "ng2-charts": "^4.1.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "uuid": "^10.0.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.11", "@angular/cli": "^19.2.11", "@angular/compiler-cli": "^19.2.9", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/canvas-confetti": "^1.9.0", "@types/googlemaps": "3.39.2", "@types/jasmine": "~5.1.0", "@types/lodash.groupby": "^4.6.9", "@types/lodash.isstring": "^4.0.9", "@types/lodash.mean": "^4.1.9", "@types/lodash.range": "^3.2.9", "@types/lodash.sortedindexby": "^4.6.9", "@types/uuid": "^8.3.4", "autoprefixer": "^10.4.20", "eslint": "^8.48.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lodash.range": "^3.2.0", "postcss": "^8.4.41", "prettier": "^3.3.3", "prettier-plugin-organize-attributes": "^1.0.0", "prettier-plugin-tailwindcss": "^0.6.2", "tailwindcss": "^3.4.10", "typescript": "~5.8.3"}, "name": "@malou-io/app-maloupe", "private": true, "scripts": {"build": "ng build", "build-clean": "rm -rf .angular && rm -rf .turbo && rm -f tsconfig.tsbuildinfo", "build-development": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --aot --configuration=development", "build-production": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --aot --configuration=production", "build-staging": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --aot --configuration=staging", "format": "prettier --write \"**/*.{ts,html,scss,json}\"", "format:check": "prettier --check \"**/*.{ts,html,scss,json}\"", "lint": "eslint --color -c .eslintrc.js --ext .ts ./src/app", "lint-fix": "eslint \"**/*.{ts,js}\" --fix", "lint-modified-files": "git status --porcelain -uall | grep -v D | cut -c4- | cut -d '>' -f 3 | grep -E '*.ts?$' | xargs eslint --color -c .eslintrc.js --ext .ts", "lint-staged": "lint-staged --no-stash", "ng": "ng", "start": "ng serve", "start-dev": "ng serve --configuration development", "start-local": "ng serve", "test": "ng test", "watch": "ng build --watch"}, "version": "0.0.0"}