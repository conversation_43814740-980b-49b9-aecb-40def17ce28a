import { ChangeDetectionStrategy, Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

import { Locale } from '@malou-io/package-utils';

import { MenuSelectComponent } from ':shared/components/menu-select/menu-select.component';
import { MALOU_SITE_URL } from ':shared/constants';
import { OtherLocalStorageKey } from ':shared/enums';
import { Logo, LogoPathResolverPipe } from ':shared/pipes';

@Component({
    selector: 'app-global-header',
    templateUrl: './global-header.component.html',
    styleUrls: ['./global-header.component.scss'],
    imports: [MenuSelectComponent, LogoPathResolverPipe],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GlobalHeaderComponent implements OnInit {
    private readonly _translateService = inject(TranslateService);
    private readonly _activatedRoute = inject(ActivatedRoute);

    readonly Logo = Logo;

    langs = [Locale.FR, Locale.EN].map((lang) => ({
        value: lang,
        viewValue: lang,
    }));
    currentLang = this._translateService.currentLang;

    ngOnInit(): void {
        this._activatedRoute.queryParamMap.subscribe((queryParams) => {
            const lang = queryParams.get('lang');
            if (lang) {
                this.changeLang({ value: lang });
            }
        });
    }

    changeLang(event: any): void {
        const lang = event.value;
        const storedLang = localStorage.getItem(OtherLocalStorageKey.LANG);
        if (storedLang !== lang) {
            localStorage.setItem(OtherLocalStorageKey.LANG, lang);
            window.location.replace(location.pathname + location.search);
        }
    }

    redirectToMalouWebSite(): void {
        window.open(MALOU_SITE_URL, '_blank');
    }
}
