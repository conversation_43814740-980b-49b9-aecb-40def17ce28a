import 'reflect-metadata';

import ':env';

import { DateTime } from 'luxon';
import assert from 'node:assert';
import { autoInjectable, container } from 'tsyringe';

import { DayMonthYear } from '@malou-io/package-utils';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@autoInjectable()
class AddDateFieldTask {
    constructor(
        private readonly _calendarEventsRepository: CalendarEventsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute() {
        console.log('Start');
        const eventsWithNullDate = await this._calendarEventsRepository.find({
            filter: { date: null },
            options: { lean: true, limit: 10000 },
        });

        let errors = 0;
        for (const event of eventsWithNullDate) {
            console.log('event', event._id.toString());
            const startDate = event['startDate'] as Date;
            const oldDateTime = DateTime.fromJSDate(startDate).toObject();
            assert(oldDateTime.day);
            assert(oldDateTime.month);
            assert(oldDateTime.year);
            const date: DayMonthYear = {
                day: oldDateTime.day,
                month: oldDateTime.month,
                year: oldDateTime.year,
            };

            await this._calendarEventsRepository.updateOne({ filter: { _id: event._id }, update: { $set: { date } } });
        }
        console.log('End, errors:', errors);
    }
}

const task = container.resolve(AddDateFieldTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
