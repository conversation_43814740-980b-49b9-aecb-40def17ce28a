import 'reflect-metadata';

import ':env';

import { autoInjectable, container } from 'tsyringe';

import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@autoInjectable()
class DeleteEventsWithNullCountryTask {
    constructor(
        private readonly _calendarEventsRepository: CalendarEventsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute() {
        console.log('Start');
        const eventsWithNullCountry = await this._calendarEventsRepository.find({
            filter: { country: null },
            options: { lean: true },
        });

        for (const event of eventsWithNullCountry) {
            await this._restaurantsRepository.updateMany({
                filter: { calendarEvents: event._id },
                update: { $pull: { calendarEvents: event._id } },
            });
            await this._calendarEventsRepository.deleteOne({ filter: { _id: event._id } });
        }
        console.log('End');
    }
}

const task = container.resolve(DeleteEventsWithNullCountryTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
