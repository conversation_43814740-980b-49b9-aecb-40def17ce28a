import { singleton } from 'tsyringe';

import { OrganizationDto } from '@malou-io/package-dto';
import { IOrganization } from '@malou-io/package-models';

@singleton()
export class OrganizationsDtoMapper {
    toOrganizationDto(organization: IOrganization): OrganizationDto {
        return {
            id: organization._id.toString(),
            name: organization.name,
            limit: organization.limit,
            verifiedEmailsForCampaigns: organization.verifiedEmailsForCampaigns,
            createdAt: organization.createdAt.toISOString(),
            updatedAt: organization.updatedAt.toISOString(),
        };
    }
}
