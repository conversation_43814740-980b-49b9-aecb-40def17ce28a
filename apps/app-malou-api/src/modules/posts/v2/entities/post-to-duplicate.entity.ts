import { PostToDuplicateDto } from '@malou-io/package-dto';
import {
    ApplicationLanguage,
    EntityConstructor,
    PlatformKey,
    PostCallToActionType,
    PostPublicationStatus,
    PostSource,
    PostType,
} from '@malou-io/package-utils';

import { SocialPostHashtag } from ':modules/posts/entities/social-post-hashtag.entity';
import { SocialPostMedia } from ':modules/posts/v2/entities/social-post-media.entity';

type PostToDuplicateProps = EntityConstructor<PostToDuplicate> & { id: string };

export class PostToDuplicate {
    id: string;
    postType: PostType;
    source: PostSource;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    text: string;
    plannedPublicationDate: Date | null;
    medias: SocialPostMedia[];
    language: ApplicationLanguage | null;
    hashtags?: {
        selected: SocialPostHashtag[];
        suggested: SocialPostHashtag[];
    };
    callToAction?: {
        actionType: PostCallToActionType;
        url?: string;
    } | null;

    constructor(data: PostToDuplicateProps) {
        this.id = data.id;
        this.postType = data.postType;
        this.source = data.source;
        this.platformKeys = data.platformKeys;
        this.published = data.published;
        this.text = data.text;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.medias = data.medias;
        this.language = data.language;
        this.hashtags = data.hashtags;
        this.callToAction = data.callToAction;
    }

    toDto(): PostToDuplicateDto {
        return {
            id: this.id,
            postType: this.postType,
            source: this.source,
            platformKeys: this.platformKeys,
            published: this.published,
            text: this.text,
            plannedPublicationDate: this.plannedPublicationDate?.toISOString() ?? null,
            medias: this.medias.map((media) => media.toDto()),
            language: this.language,
            hashtags: this.hashtags,
            callToAction: this.callToAction ?? null,
        };
    }
}
