import { NextFunction, Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import {
    CreatePostBodyDto,
    CreatePostParamsDto,
    CreateStoryBodyDto,
    CreateStoryDto,
    DuplicatePostBodyDto,
    DuplicateSeoPostWithAiBodyDto,
    DuplicateSocialPostWithAiBodyDto,
    GetPostsWithInsightsBodyDto,
    GetRestaurantPostsBodyDto,
    GetRestaurantPostsResponseDto,
    GetTiktokQueryCreatorInfoDto,
    PlatformKeysBodyDto,
    RestaurantIdParamsTransformDto,
    UpdatePostParamsDto,
    UpdatePostPayloadDto,
} from '@malou-io/package-dto';
import { ApiResultV2, PostPublicationStatus, TimeInSeconds } from '@malou-io/package-utils';

import { casl } from ':helpers/casl/middlewares';
import { RequestWithExperimentation, RequestWithPermissions } from ':helpers/utils.types';
import { platformsExist } from ':modules/platforms/platforms.middlewares';
import { cacheMiddleware, CachePrefixKey } from ':plugins/cache-middleware';
import { authorize } from ':plugins/passport';
import { experimentationMiddleware } from ':services/experimentations-service/experimentation.service';

import PostsController from './posts.controller';
import {
    bindRequiredDataFromPostIdParams,
    bindRequiredDataFromPostIdsParams,
    isDraftPost,
    userCanCreatePost,
    userCanPublishPost,
} from './posts.middlewares';

@singleton()
export default class PostsRouter {
    constructor(private _postsController: PostsController) {}

    init(router: Router) {
        router.get('/posts/platforms/:platform_id/igsearch', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleSearchIgAccounts(req, res, next)
        );

        router.get('/posts/platforms/:platform_id/igoembed/:social_link', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetIgOembed(req, res, next)
        );

        router.post(
            '/posts/restaurants/:restaurant_id/synchronize',
            authorize(),
            (req: Request<RestaurantIdParamsTransformDto, any, PlatformKeysBodyDto>, res: Response, next: NextFunction) =>
                this._postsController.handleSynchronizePosts(req, res, next)
        );

        router.post(
            '/posts/restaurants/:restaurant_id/all',
            authorize(),
            (req: RequestWithExperimentation<false>, res: Response, next: NextFunction) =>
                experimentationMiddleware({ req, res, next, featureName: 'release-tiktok-platform', shouldRaiseError: false }),
            (
                req: RequestWithExperimentation<any, RestaurantIdParamsTransformDto, any, GetRestaurantPostsBodyDto>,
                res: Response<ApiResultV2<GetRestaurantPostsResponseDto>>,
                next: NextFunction
            ) => this._postsController.handleGetRestaurantPosts(req, res, next)
        );

        // TODO: this endpoint is for the Mobile App, it should be removed later [@Mobile]
        router.get(
            '/posts/restaurants/:restaurant_id',
            authorize(),
            (req: RequestWithExperimentation<false>, res: Response, next: NextFunction) =>
                experimentationMiddleware({ req, res, next, featureName: 'release-tiktok-platform', shouldRaiseError: false }),
            (req: RequestWithExperimentation, res: Response, next: NextFunction) =>
                this._postsController.handleGetRestaurantPostsForMobile(req, res, next)
        );

        router.get(
            '/posts/by_binding_id',
            authorize(),
            (req: Request<null, null, null, { binding_id: string; binding_id_key: string }>, res: Response, next: NextFunction) =>
                this._postsController.handleGetPostByBindingId(req, res, next)
        );

        router.get('/posts/:post_id', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetPost(req, res, next)
        );

        router.get('/posts/:post_id/stories', authorize(), (req: Request<{ post_id: string }>, res: Response, next: NextFunction) =>
            this._postsController.handleGetPostStories(req, res, next)
        );

        router.get('/posts/:post_id/refresh', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleRefreshPost(req, res, next)
        );

        router.delete('/posts/:post_id/jobs', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleDeletePostJobs(req, res, next)
        );

        router.post(
            '/posts/restaurants/:restaurant_id',
            authorize(),
            casl(),
            userCanCreatePost(),
            (req: Request<CreatePostParamsDto, any, CreatePostBodyDto>, res: Response, next: NextFunction) =>
                this._postsController.handleCreatePost(req, res, next)
        );

        router.post(
            '/posts/restaurants/:restaurant_id/duplicate',
            authorize(),
            isDraftPost(),
            platformsExist('body.keys'),
            casl(),
            [userCanCreatePost(), userCanPublishPost()],
            (req: RequestWithPermissions<CreatePostParamsDto, any, DuplicatePostBodyDto>, res: Response, next: NextFunction) =>
                this._postsController.handleDuplicatePost(req, res, next)
        );

        router.post(
            '/posts/duplicate_seo_posts_with_ai',
            authorize(),
            casl(),
            (req: RequestWithPermissions<any, any, DuplicateSeoPostWithAiBodyDto>, res: Response, next: NextFunction) =>
                this._postsController.handleDuplicateSeoPostWithAi(req, res, next)
        );

        router.post(
            '/posts/duplicate_social_posts_with_ai',
            authorize(),
            casl(),
            (req: RequestWithPermissions<any, any, DuplicateSocialPostWithAiBodyDto>, res: Response, next: NextFunction) =>
                this._postsController.handleDuplicateSocialPostWithAi(req, res, next)
        );

        router.post(
            '/posts/restaurants/:restaurant_id/stories',
            authorize(),
            (req: Request<CreateStoryDto, any, CreateStoryBodyDto>, res: Response, next: NextFunction) =>
                this._postsController.handleCreateStory(req, res, next)
        );

        router.post('/posts/stories/prepare', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handlePrepareStory(req, res, next)
        );

        router.post('/posts/stories/cancel', authorize(), casl(), (req: RequestWithPermissions, res: Response, next: NextFunction) =>
            this._postsController.handleCancelStory(req, res, next)
        );

        router.get(
            '/posts/restaurants/:restaurant_id/stories/synchronize',
            authorize(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) =>
                this._postsController.handleSynchronizeStories(req, res, next)
        );

        router.put(
            '/posts/:post_id/prepare',
            authorize(),
            bindRequiredDataFromPostIdParams(),
            casl(),
            (
                req: RequestWithPermissions<UpdatePostParamsDto> & {
                    currentPost?: { published: PostPublicationStatus; key: any };
                },
                res: Response,
                next: NextFunction
            ) => this._postsController.handlePreparePost(req, res, next)
        );

        /**
         * Reorder a set of posts in the feed by reassigning scheduled publication dates
         * (plannedPublicationDates).
         *
         * This endpoint is designed to allow to move posts in the feed with drag-and-drop.
         */
        router.post(
            '/posts/swap-planned-publication-dates',
            authorize(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) =>
                this._postsController.swapPlannedPublicationDates(req, res, next)
        );

        router.put(
            '/posts/:post_id',
            authorize(),
            bindRequiredDataFromPostIdParams(),
            casl(),
            (req: RequestWithPermissions<UpdatePostParamsDto, never, UpdatePostPayloadDto>, res: Response, next: NextFunction) =>
                this._postsController.handleUpdatePost(req, res, next)
        );

        router.delete(
            '/posts/:post_id',
            authorize(),
            bindRequiredDataFromPostIdParams(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) => this._postsController.handleDeletePost(req, res, next)
        );

        router.post(
            '/posts/delete',
            authorize(),
            bindRequiredDataFromPostIdsParams(),
            casl(),
            (req: RequestWithPermissions, res: Response, next: NextFunction) => this._postsController.handleDeletePosts(req, res, next)
        );

        router.post(
            '/posts/restaurants/:restaurant_id/insights',
            authorize(),
            platformsExist('body.platformKeys'),
            cacheMiddleware(CachePrefixKey.GET_RESTAURANT_POSTS_INSIGHTS, 10 * TimeInSeconds.MINUTE),
            (req: Request<RestaurantIdParamsTransformDto, any, GetPostsWithInsightsBodyDto>, res: Response, next: NextFunction) =>
                this._postsController.handleFetchPostsWithInsights(req, res, next)
        );

        router.post(
            '/posts/get-top-3-posts-insights-v2',
            authorize(),
            cacheMiddleware(CachePrefixKey.GET_TOP_3_POSTS_INSIGHTS, TimeInSeconds.HOUR),
            (req: Request, res: Response, next: NextFunction) => this._postsController.handleGetTop3PostsInsightsV2(req, res, next)
        );

        router.get('/posts/platforms/:platform_id/competitors', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetCompetitorsPosts(req, res, next)
        );

        router.get('/posts/restaurants/:restaurant_id/lastsocial', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetLastSocialPosts(req, res, next)
        );

        router.get(
            '/posts/restaurants/:restaurant_id/date/:start_date/:end_date',
            authorize(),
            (req: Request, res: Response, next: NextFunction) => this._postsController.handleGetPostsByDateRange(req, res, next)
        );

        router.post('/posts/social_ids', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetPostsBySocialIds(req, res, next)
        );

        router.get('/posts/restaurants/:restaurant_id/iglast/:number', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetLastIgPosts(req, res, next)
        );

        router.get(
            '/posts/restaurants/:restaurant_id/platforms/:platform_key',
            authorize(),
            (req: Request, res: Response, next: NextFunction) => this._postsController.handleGetPostsByPlatform(req, res, next)
        );

        router.get('/posts/restaurants/:restaurant_id/feedbacks/:type', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetPostsByFeedbackType(req, res, next)
        );

        router.post('/posts/restaurants/:restaurant_id/media', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetPostsByMedia(req, res, next)
        );

        router.post('/posts/deleted_media', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleUpdatePostMedias(req, res, next)
        );

        router.get('/posts/stories/count', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleGetStoriesByAuthorCount(req, res, next)
        );

        router.post('/posts/attachments/resize', authorize(), (req: Request, res: Response, next: NextFunction) =>
            this._postsController.handleResizeAttachments(req, res, next)
        );

        /**
         *
         * Tiktok
         *
         */
        router.get(
            '/posts/restaurants/:restaurant_id/tiktok/query_creator_info',
            authorize(),
            (
                req: Request<RestaurantIdParamsTransformDto, any, any>,
                res: Response<ApiResultV2<GetTiktokQueryCreatorInfoDto>>,
                next: NextFunction
            ) => this._postsController.tiktokQueryCreatorInfo(req, res, next)
        );
    }
}
