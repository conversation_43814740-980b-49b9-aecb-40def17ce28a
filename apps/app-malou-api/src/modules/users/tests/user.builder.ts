import { Builder } from 'builder-pattern';

import { IUser, IUserRestaurant, newDbId, toDbId } from '@malou-io/package-models';
import { ApplicationLanguage, CaslRole, Role } from '@malou-io/package-utils';

const _buildUser = (user: IUser) => Builder<IUser>(user);
const _buildUserRestaurant = (userRestaurant: IUserRestaurant) => Builder<IUserRestaurant>(userRestaurant);

export const defaultUserSettings = {
    receiveFeedbacks: true,
    notifications: {
        email: {
            reviewReplyReminder: {
                active: true,
            },
            specialHourReminder: {
                active: true,
            },
            postSuggestion: {
                active: true,
            },
            postError: {
                active: true,
            },
            roiActivated: {
                active: true,
            },
            summary: {
                active: true,
            },
            platformDisconnected: {
                active: true,
            },
        },
        web: {
            showFloatingToast: true,
            filters: {
                restaurantIds: [],
            },
            newReviews: {
                active: true,
            },
            reviewReplyReminder: { active: true },
            specialHourReminder: {
                active: true,
            },
            postSuggestion: {
                active: true,
            },
            postError: {
                active: true,
            },
            newMessage: {
                active: true,
            },
            roiActivated: {
                active: true,
            },
            platformDisconnected: {
                active: true,
            },
            informationUpdateError: {
                active: true,
            },
        },
        mobile: {
            userDevicesTokens: [],
            active: true,
            newMessage: {
                active: true,
                realtime: true,
                receivingWeekDays: [1, 2, 3, 4, 5, 6, 7],
            },
            newReviews: {
                active: true,
                realtime: true,
                receivingWeekDays: [1, 2, 3, 4, 5],
                concernedRatings: [1, 2, 3, 4, 5],
                includeAutoRepliedReviews: true,
            },
            noMoreScheduledPosts: {
                active: true,
            },
        },
    },
    receiveMessagesNotifications: {
        active: true,
        restaurantsIds: [],
    },
    notificationSettings: {
        active: true,
        userDevicesTokens: ['token'],
        messages: {
            active: true,
            realtime: true,
            receivingWeekDays: [1, 2, 3, 4, 5],
        },
        reviews: {
            active: true,
            realtime: true,
            concernedRatings: [1, 2, 3, 4, 5],
            includeAutoRepliedReviews: true,
            receivingWeekDays: [1, 2, 3, 4, 5],
        },
        posts: {
            noMoreScheduledPosts: {
                active: true,
                lastNotificationSentDate: new Date(),
            },
            publishError: {
                active: true,
            },
        },
    },
};

export const getDefaultUser = () =>
    _buildUser({
        _id: newDbId(),
        name: 'John',
        email: '<EMAIL>',
        password: 'password',
        createdAt: new Date(),
        updatedAt: new Date(),
        organizationIds: [toDbId('5f3e2a1a0b1f7a001b0e6b0d')],
        lastname: 'Doe',
        profilePicture: toDbId('5f3e2a1a0b1f7a001b0e6b0d'),
        role: Role.MALOU_BASIC,
        caslRole: CaslRole.OWNER,
        verified: true,
        createdByUserId: toDbId('5f3e2a1a0b1f7a001b0e6b0d'),
        defaultLanguage: ApplicationLanguage.EN,
        expireSessionBefore: new Date(),
        shouldExpireAbilitySession: true,
        hasV3Access: true,
        settings: defaultUserSettings,
        lastVisitedRestaurantId: undefined,
        hasBeenDeactivatedByAdmin: false,
    });

export const getDefaultUserRestaurant = () =>
    _buildUserRestaurant({
        _id: newDbId(),
        caslRole: CaslRole.OWNER,
        createdAt: new Date(),
        updatedAt: new Date(),
        userId: newDbId(),
        restaurantId: newDbId(),
        apiEndpoint: '',
        gmbCredentialId: newDbId(),
        fbCredentialId: newDbId(),
        displayPermissionsModal: false,
        displayHashtagsModal: false,
        displaySyncWarningModal: false,
        lastConnectionEmailSent: new Date(),
        displaySemanticAnalyses: false,
    });
