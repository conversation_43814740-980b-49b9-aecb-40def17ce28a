import { container } from 'tsyringe';

import { newDbId, toDbId } from '@malou-io/package-models';
import { AiInteractionRelatedEntityCollection, AiInteractionType, ChatCompletionRole, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { AiBrandKeywordsIdentificationService } from ':microservices/ai-brand-keywords-identification.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { BrandKeywordsIdentificationService } from ':modules/keyword-search-impressions/services/brand-keywords-identification/brand-keywords-identification.service';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

describe('BrandKeywordsIdentificationService', () => {
    const lambdaSuccessResponse = {
        aiResponse: {
            brandNameKeywords: ['restaurant name', 'name restaurant'],
            brandGroupKeywords: ['restaurant group', 'group restaurant'],
        },
        aiInteractionDetails: [{ prompt: 'test prompt', response: 'test response' }],
    };

    class AiBrandKeywordsIdentificationServiceMock {
        async processBrandKeywordsIdentification(_params: any) {
            return Promise.resolve(lambdaSuccessResponse);
        }
    }

    let brandKeywordsIdentificationService: BrandKeywordsIdentificationService;
    let restaurantsRepository: RestaurantsRepository;
    let aiInteractionsRepository: AiInteractionsRepository;

    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'OrganizationsRepository']);

        container.register(AiBrandKeywordsIdentificationService, {
            useValue: new AiBrandKeywordsIdentificationServiceMock() as any,
        });

        brandKeywordsIdentificationService = container.resolve(BrandKeywordsIdentificationService);
        restaurantsRepository = container.resolve(RestaurantsRepository);
        aiInteractionsRepository = container.resolve(AiInteractionsRepository);
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('identifyBrandKeywordsForRestaurant', () => {
        it('should identify brand keywords for a restaurant and update in database', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Test Organization').build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurant().name('Test Restaurant').organizationId(dependencies.organizations()[0]._id).build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();

            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const findOneAndUpdateSpy = jest.spyOn(restaurantsRepository, 'findOneAndUpdate');

            const result = await brandKeywordsIdentificationService.identifyBrandKeywordsForRestaurant(restaurantId);

            expect(result).toEqual({
                brandNameKeywords: lambdaSuccessResponse.aiResponse.brandNameKeywords,
                brandGroupKeywords: lambdaSuccessResponse.aiResponse.brandGroupKeywords,
            });

            expect(findOneAndUpdateSpy).toHaveBeenCalledWith({
                filter: { _id: toDbId(restaurantId) },
                update: {
                    brandKeywords: {
                        brandNameKeywords: lambdaSuccessResponse.aiResponse.brandNameKeywords,
                        brandGroupKeywords: lambdaSuccessResponse.aiResponse.brandGroupKeywords,
                    },
                },
            });

            const updatedRestaurant = await restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                options: { lean: true },
                projection: { brandKeywords: 1 },
            });

            expect(updatedRestaurant?.brandKeywords).toEqual({
                brandNameKeywords: lambdaSuccessResponse.aiResponse.brandNameKeywords,
                brandGroupKeywords: lambdaSuccessResponse.aiResponse.brandGroupKeywords,
            });
        });

        it('should throw an error if restaurant is not found', async () => {
            const nonExistentRestaurantId = newDbId().toString();

            await expect(brandKeywordsIdentificationService.identifyBrandKeywordsForRestaurant(nonExistentRestaurantId)).rejects.toThrow();
        });

        it('should handle general errors and update the AI interaction with error details', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Test Organization').build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurant().name('Test Restaurant').organizationId(dependencies.organizations()[0]._id).build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();

            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const testError = new MalouError(MalouErrorCode.INTERNAL_SERVER_ERROR, {
                message: 'Test error message',
            });

            const aiBrandKeywordsIdentificationService = container.resolve(AiBrandKeywordsIdentificationService);
            jest.spyOn(aiBrandKeywordsIdentificationService, 'processBrandKeywordsIdentification').mockImplementationOnce(() => {
                throw testError;
            });

            await expect(brandKeywordsIdentificationService.identifyBrandKeywordsForRestaurant(restaurantId)).rejects.toThrow(testError);

            const aiInteraction = await aiInteractionsRepository.findOne({
                filter: { type: AiInteractionType.BRAND_KEYWORDS_IDENTIFICATION, restaurantId: toDbId(restaurantId) },
                options: { lean: true },
            });

            expect(aiInteraction?.error).toEqual({
                malouErrorCode: testError.malouErrorCode,
                message: testError.message,
                stack: testError.stack,
            });
        });

        it('should update restaurant with empty brand keywords if lambda returns empty arrays', async () => {
            const emptyLambdaResponse = {
                aiResponse: {
                    brandNameKeywords: [],
                    brandGroupKeywords: [],
                },
                aiInteractionDetails: [
                    {
                        type: AiInteractionType.BRAND_KEYWORDS_IDENTIFICATION,
                        relatedEntityCollection: AiInteractionRelatedEntityCollection.KEYWORDS,
                        message: [{ system: 'system prompt', user: 'user prompt' }],
                        completionText: 'test response',
                        completionTokenCount: 10,
                        promptTokenCount: 20,
                        prompt: 'test prompt',
                        response: 'test response',
                        rawCompletionText: 'test response',
                        completionTimeInMilliseconds: 300,
                        responseTimeInMilliseconds: 400,
                        messages: [
                            { role: ChatCompletionRole.SYSTEM, text: 'system prompt' },
                            { role: ChatCompletionRole.USER, text: 'user prompt' },
                        ],
                    },
                ],
            };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name('Empty Organization').build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurant().name('Empty Restaurant').organizationId(dependencies.organizations()[0]._id).build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();

            const restaurantId = seededObjects.restaurants[0]._id.toString();

            const aiBrandKeywordsIdentificationService = container.resolve(AiBrandKeywordsIdentificationService);
            jest.spyOn(aiBrandKeywordsIdentificationService, 'processBrandKeywordsIdentification').mockResolvedValueOnce(
                emptyLambdaResponse
            );

            const result = await brandKeywordsIdentificationService.identifyBrandKeywordsForRestaurant(restaurantId);

            expect(result).toEqual({
                brandNameKeywords: [],
                brandGroupKeywords: [],
            });

            const updatedRestaurant = await restaurantsRepository.findOne({
                filter: { _id: toDbId(restaurantId) },
                options: { lean: true },
                projection: { brandKeywords: 1 },
            });

            expect(updatedRestaurant?.brandKeywords).toEqual({
                brandNameKeywords: [],
                brandGroupKeywords: [],
            });
        });
    });
});
