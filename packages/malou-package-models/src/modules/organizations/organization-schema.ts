import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const organizationJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Organization',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        name: {
            type: 'string',
            minLength: 1,
        },
        verifiedEmailsForCampaigns: {
            type: 'array',
            items: {
                type: 'string',
            },
            default: [],
        },
        limit: {
            type: 'integer',
            nullable: true,
            default: 1,
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: ['_id', 'name', 'createdAt', 'updatedAt'],
    definitions: {},
} as const satisfies JSONSchemaExtraProps;
